<!DOCTYPE import-control PUBLIC
        "-//Puppy Crawl//DTD Import Control 1.1//EN"
        "http://www.puppycrawl.com/dtds/import_control_1_1.dtd">
<!--
// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
-->

<import-control pkg="org.apache.kafka.jmh">

    <allow pkg="java"/>
    <allow pkg="scala"/>
    <allow pkg="javax.management"/>
    <allow pkg="org.slf4j"/>
    <allow pkg="org.openjdk.jmh.annotations"/>
    <allow pkg="org.openjdk.jmh.runner"/>
    <allow pkg="org.openjdk.jmh.infra"/>
    <allow pkg="java.security"/>
    <allow pkg="javax.net.ssl"/>
    <allow pkg="javax.security"/>
    <allow pkg="org.apache.kafka.common"/>
    <allow pkg="org.apache.kafka.clients.producer"/>
    <allow pkg="kafka.cluster"/>
    <allow pkg="kafka.log"/>
    <allow pkg="kafka.server"/>
    <allow pkg="kafka.api"/>
    <allow class="kafka.utils.Pool"/>
    <allow class="kafka.utils.KafkaScheduler"/>
    <allow class="org.apache.kafka.clients.FetchSessionHandler"/>
    <allow pkg="org.mockito"/>


    <subpackage name="cache">
    </subpackage>
</import-control>
