# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Use this example Vagrantfile.local for running system tests
# To use it, move it to the base kafka directory and rename
# it to Vagrantfile.local
num_zookeepers = 0
num_brokers = 0
num_workers = 9
base_box = "kafkatest-worker"


# System tests use hostnames for each worker that need to be defined in /etc/hosts on the host running ducktape
enable_dns = true
