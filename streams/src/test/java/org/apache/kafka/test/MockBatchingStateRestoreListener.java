/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements. See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.kafka.test;

import org.apache.kafka.streams.KeyValue;
import org.apache.kafka.streams.processor.BatchingStateRestoreCallback;

import java.util.ArrayList;
import java.util.Collection;

public class MockBatchingStateRestoreListener extends MockStateRestoreListener implements BatchingStateRestoreCallback {

    private final Collection<KeyValue<byte[], byte[]>> restoredRecords = new ArrayList<>();

    @Override
    public void restoreAll(final Collection<KeyValue<byte[], byte[]>> records) {
        restoredRecords.addAll(records);
    }

    @Override
    public void restore(final byte[] key, final byte[] value) {
        throw new IllegalStateException("Should not be called");

    }

    public void resetRestoredBatch() {
        restoredRecords.clear();
    }

    public Collection<KeyValue<byte[], byte[]>> getRestoredRecords() {
        return restoredRecords;
    }
}
