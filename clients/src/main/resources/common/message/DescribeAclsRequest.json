// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

{
  "apiKey": 29,
  "type": "request",
  "name": "DescribeAclsRequest",
  // Version 1 adds resource pattern type.
  // Version 2 enables flexible versions.
  "validVersions": "0-2",
  "flexibleVersions": "2+",
  "fields": [
    { "name": "ResourceTypeFilter", "type": "int8", "versions": "0+",
      "about": "The resource type." },
    { "name": "ResourceNameFilter", "type": "string", "versions": "0+", "nullableVersions": "0+",
      "about": "The resource name, or null to match any resource name." },
    { "name": "PatternTypeFilter", "type": "int8", "versions": "1+", "default": "3", "ignorable": false,
      "about": "The resource pattern to match." },
    { "name": "PrincipalFilter", "type": "string", "versions": "0+", "nullableVersions": "0+",
      "about": "The principal to match, or null to match any principal." },
    { "name": "HostFilter", "type": "string", "versions": "0+", "nullableVersions": "0+",
      "about": "The host to match, or null to match any host." },
    { "name": "Operation", "type": "int8", "versions": "0+",
      "about": "The operation to match." },
    { "name": "PermissionType", "type": "int8", "versions": "0+",
      "about": "The permission type to match." }
  ]
}
