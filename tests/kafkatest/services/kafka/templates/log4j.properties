# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

log4j.rootLogger={{ log_level|default("DEBUG") }}, stdout

log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=[%d] %p %m (%c)%n

# INFO level appenders
log4j.appender.kafkaInfoAppender=org.apache.log4j.DailyRollingFileAppender
log4j.appender.kafkaInfoAppender.DatePattern='.'yyyy-MM-dd-HH
log4j.appender.kafkaInfoAppender.File={{ log_dir }}/info/server.log
log4j.appender.kafkaInfoAppender.layout=org.apache.log4j.PatternLayout
log4j.appender.kafkaInfoAppender.layout.ConversionPattern=[%d] %p %m (%c)%n
log4j.appender.kafkaInfoAppender.Threshold=INFO

log4j.appender.stateChangeInfoAppender=org.apache.log4j.DailyRollingFileAppender
log4j.appender.stateChangeInfoAppender.DatePattern='.'yyyy-MM-dd-HH
log4j.appender.stateChangeInfoAppender.File={{ log_dir }}/info/state-change.log
log4j.appender.stateChangeInfoAppender.layout=org.apache.log4j.PatternLayout
log4j.appender.stateChangeInfoAppender.layout.ConversionPattern=[%d] %p %m (%c)%n
log4j.appender.stateChangeInfoAppender.Threshold=INFO

log4j.appender.requestInfoAppender=org.apache.log4j.DailyRollingFileAppender
log4j.appender.requestInfoAppender.DatePattern='.'yyyy-MM-dd-HH
log4j.appender.requestInfoAppender.File={{ log_dir }}/info/kafka-request.log
log4j.appender.requestInfoAppender.layout=org.apache.log4j.PatternLayout
log4j.appender.requestInfoAppender.layout.ConversionPattern=[%d] %p %m (%c)%n
log4j.appender.requestInfoAppender.Threshold=INFO

log4j.appender.cleanerInfoAppender=org.apache.log4j.DailyRollingFileAppender
log4j.appender.cleanerInfoAppender.DatePattern='.'yyyy-MM-dd-HH
log4j.appender.cleanerInfoAppender.File={{ log_dir }}/info/log-cleaner.log
log4j.appender.cleanerInfoAppender.layout=org.apache.log4j.PatternLayout
log4j.appender.cleanerInfoAppender.layout.ConversionPattern=[%d] %p %m (%c)%n
log4j.appender.cleanerInfoAppender.Threshold=INFO

log4j.appender.controllerInfoAppender=org.apache.log4j.DailyRollingFileAppender
log4j.appender.controllerInfoAppender.DatePattern='.'yyyy-MM-dd-HH
log4j.appender.controllerInfoAppender.File={{ log_dir }}/info/controller.log
log4j.appender.controllerInfoAppender.layout=org.apache.log4j.PatternLayout
log4j.appender.controllerInfoAppender.layout.ConversionPattern=[%d] %p %m (%c)%n
log4j.appender.controllerInfoAppender.Threshold=INFO

log4j.appender.authorizerInfoAppender=org.apache.log4j.DailyRollingFileAppender
log4j.appender.authorizerInfoAppender.DatePattern='.'yyyy-MM-dd-HH
log4j.appender.authorizerInfoAppender.File={{ log_dir }}/info/kafka-authorizer.log
log4j.appender.authorizerInfoAppender.layout=org.apache.log4j.PatternLayout
log4j.appender.authorizerInfoAppender.layout.ConversionPattern=[%d] %p %m (%c)%n
log4j.appender.authorizerInfoAppender.Threshold=INFO

# DEBUG level appenders
log4j.appender.kafkaDebugAppender=org.apache.log4j.DailyRollingFileAppender
log4j.appender.kafkaDebugAppender.DatePattern='.'yyyy-MM-dd-HH
log4j.appender.kafkaDebugAppender.File={{ log_dir }}/debug/server.log
log4j.appender.kafkaDebugAppender.layout=org.apache.log4j.PatternLayout
log4j.appender.kafkaDebugAppender.layout.ConversionPattern=[%d] %p %m (%c)%n
log4j.appender.kafkaDebugAppender.Threshold=DEBUG

log4j.appender.stateChangeDebugAppender=org.apache.log4j.DailyRollingFileAppender
log4j.appender.stateChangeDebugAppender.DatePattern='.'yyyy-MM-dd-HH
log4j.appender.stateChangeDebugAppender.File={{ log_dir }}/debug/state-change.log
log4j.appender.stateChangeDebugAppender.layout=org.apache.log4j.PatternLayout
log4j.appender.stateChangeDebugAppender.layout.ConversionPattern=[%d] %p %m (%c)%n
log4j.appender.stateChangeDebugAppender.Threshold=DEBUG

log4j.appender.requestDebugAppender=org.apache.log4j.DailyRollingFileAppender
log4j.appender.requestDebugAppender.DatePattern='.'yyyy-MM-dd-HH
log4j.appender.requestDebugAppender.File={{ log_dir }}/debug/kafka-request.log
log4j.appender.requestDebugAppender.layout=org.apache.log4j.PatternLayout
log4j.appender.requestDebugAppender.layout.ConversionPattern=[%d] %p %m (%c)%n
log4j.appender.requestDebugAppender.Threshold=DEBUG

log4j.appender.cleanerDebugAppender=org.apache.log4j.DailyRollingFileAppender
log4j.appender.cleanerDebugAppender.DatePattern='.'yyyy-MM-dd-HH
log4j.appender.cleanerDebugAppender.File={{ log_dir }}/debug/log-cleaner.log
log4j.appender.cleanerDebugAppender.layout=org.apache.log4j.PatternLayout
log4j.appender.cleanerDebugAppender.layout.ConversionPattern=[%d] %p %m (%c)%n
log4j.appender.cleanerDebugAppender.Threshold=DEBUG

log4j.appender.controllerDebugAppender=org.apache.log4j.DailyRollingFileAppender
log4j.appender.controllerDebugAppender.DatePattern='.'yyyy-MM-dd-HH
log4j.appender.controllerDebugAppender.File={{ log_dir }}/debug/controller.log
log4j.appender.controllerDebugAppender.layout=org.apache.log4j.PatternLayout
log4j.appender.controllerDebugAppender.layout.ConversionPattern=[%d] %p %m (%c)%n
log4j.appender.controllerDebugAppender.Threshold=DEBUG

log4j.appender.authorizerDebugAppender=org.apache.log4j.DailyRollingFileAppender
log4j.appender.authorizerDebugAppender.DatePattern='.'yyyy-MM-dd-HH
log4j.appender.authorizerDebugAppender.File={{ log_dir }}/debug/kafka-authorizer.log
log4j.appender.authorizerDebugAppender.layout=org.apache.log4j.PatternLayout
log4j.appender.authorizerDebugAppender.layout.ConversionPattern=[%d] %p %m (%c)%n
log4j.appender.authorizerDebugAppender.Threshold=DEBUG

# Turn on all our debugging info
log4j.logger.kafka.producer.async.DefaultEventHandler={{ log_level|default("DEBUG") }}, kafkaInfoAppender, kafkaDebugAppender
log4j.logger.kafka.client.ClientUtils={{ log_level|default("DEBUG") }}, kafkaInfoAppender, kafkaDebugAppender
log4j.logger.kafka.perf={{ log_level|default("DEBUG") }}, kafkaInfoAppender, kafkaDebugAppender
log4j.logger.kafka.perf.ProducerPerformance$ProducerThread={{ log_level|default("DEBUG") }}, kafkaInfoAppender, kafkaDebugAppender
log4j.logger.kafka={{ log_level|default("DEBUG") }}, kafkaInfoAppender, kafkaDebugAppender

log4j.logger.kafka.network.RequestChannel$={{ log_level|default("DEBUG") }}, requestInfoAppender, requestDebugAppender
log4j.additivity.kafka.network.RequestChannel$=false

log4j.logger.kafka.network.Processor={{ log_level|default("DEBUG") }}, requestInfoAppender, requestDebugAppender
log4j.logger.kafka.server.KafkaApis={{ log_level|default("DEBUG") }}, requestInfoAppender, requestDebugAppender
log4j.additivity.kafka.server.KafkaApis=false
log4j.logger.kafka.request.logger={{ log_level|default("DEBUG") }}, requestInfoAppender, requestDebugAppender
log4j.additivity.kafka.request.logger=false

log4j.logger.kafka.controller={{ log_level|default("DEBUG") }}, controllerInfoAppender, controllerDebugAppender
log4j.additivity.kafka.controller=false

log4j.logger.kafka.log.LogCleaner={{ log_level|default("DEBUG") }}, cleanerInfoAppender, cleanerDebugAppender
log4j.additivity.kafka.log.LogCleaner=false

log4j.logger.state.change.logger={{ log_level|default("DEBUG") }}, stateChangeInfoAppender, stateChangeDebugAppender
log4j.additivity.state.change.logger=false

#Change this to debug to get the actual audit log for authorizer.
log4j.logger.kafka.authorizer.logger={{ log_level|default("DEBUG") }}, authorizerInfoAppender, authorizerDebugAppender
log4j.additivity.kafka.authorizer.logger=false

